import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Switch,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Modal, Input, Button } from "../../components/ui";
import { TroubleshootingPanel } from "../../components/connection";
import UserPreferences from "../../services/UserPreferences";

const { width, height } = Dimensions.get("window");

export default function SettingsScreen() {
  const [notifications, setNotifications] = useState(true);
  const [autoConnect, setAutoConnect] = useState(false);
  const [darkMode, setDarkMode] = useState(true);
  const [analytics, setAnalytics] = useState(true);
  const [deviceName, setDeviceName] = useState("");
  const [showEditName, setShowEditName] = useState(false);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);
  const [newDeviceName, setNewDeviceName] = useState("");
  const [userStats, setUserStats] = useState({ sessions: 0, playtime: 0 });

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const prefs = await UserPreferences.getPreferences();
      setNotifications(prefs.notificationsEnabled);
      setAutoConnect(prefs.autoScanEnabled);
      setDarkMode(prefs.theme === "dark");
      setDeviceName(prefs.deviceName);
      setNewDeviceName(prefs.deviceName);
      setUserStats({
        sessions: prefs.totalSessions,
        playtime: prefs.totalPlaytime,
      });
    } catch (error) {
      console.error("Failed to load preferences:", error);
    }
  };

  const handleNotificationsToggle = async (value: boolean) => {
    setNotifications(value);
    await UserPreferences.setNotificationsEnabled(value);
  };

  const handleAutoConnectToggle = async (value: boolean) => {
    setAutoConnect(value);
    await UserPreferences.setAutoScanEnabled(value);
  };

  const handleDarkModeToggle = async (value: boolean) => {
    setDarkMode(value);
    await UserPreferences.setTheme(value ? "dark" : "light");
  };

  const handleSaveDeviceName = async () => {
    if (newDeviceName.trim()) {
      await UserPreferences.setDeviceName(newDeviceName.trim());
      setDeviceName(newDeviceName.trim());
      setShowEditName(false);
    }
  };

  const handleResetPreferences = () => {
    Alert.alert(
      "Reset Settings",
      "This will reset all your preferences and clear your data. This action cannot be undone.",
      [
        {
          text: "Reset",
          style: "destructive",
          onPress: async () => {
            await UserPreferences.resetPreferences();
            Alert.alert(
              "Settings Reset",
              "All preferences have been reset to defaults."
            );
            loadPreferences();
          },
        },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  const formatPlaytime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const settingsGroups = [
    {
      title: "Device",
      items: [
        {
          icon: "phone-portrait",
          title: "Device Name",
          subtitle: deviceName || "Tap to set device name",
          type: "action",
          onPress: () => setShowEditName(true),
        },
        {
          icon: "stats-chart",
          title: "Gaming Sessions",
          subtitle: `${userStats.sessions} sessions completed`,
          type: "info",
        },
        {
          icon: "time",
          title: "Total Playtime",
          subtitle: formatPlaytime(userStats.playtime),
          type: "info",
        },
      ],
    },
    {
      title: "Connection",
      items: [
        {
          icon: "notifications",
          title: "Notifications",
          subtitle: "Get notified when devices are found",
          type: "switch",
          value: notifications,
          onToggle: handleNotificationsToggle,
        },
        {
          icon: "flash",
          title: "Auto Connect",
          subtitle: "Automatically connect to known devices",
          type: "switch",
          value: autoConnect,
          onToggle: handleAutoConnectToggle,
        },
        {
          icon: "time",
          title: "Connection Timeout",
          subtitle: "30 seconds",
          type: "navigation",
        },
      ],
    },
    {
      title: "Appearance",
      items: [
        {
          icon: "moon",
          title: "Dark Mode",
          subtitle: "Use dark theme",
          type: "switch",
          value: darkMode,
          onToggle: handleDarkModeToggle,
        },
        {
          icon: "color-palette",
          title: "Theme Color",
          subtitle: "Cyan Blue",
          type: "navigation",
        },
      ],
    },
    {
      title: "Privacy & Security",
      items: [
        {
          icon: "shield-checkmark",
          title: "Device Visibility",
          subtitle: "Visible to nearby devices",
          type: "navigation",
        },
        {
          icon: "analytics",
          title: "Analytics",
          subtitle: "Help improve the app",
          type: "switch",
          value: analytics,
          onToggle: setAnalytics,
        },
        {
          icon: "lock-closed",
          title: "Privacy Policy",
          subtitle: "View our privacy policy",
          type: "navigation",
        },
      ],
    },
    {
      title: "Support",
      items: [
        {
          icon: "medical",
          title: "Network Diagnostics",
          subtitle: "Run network troubleshooting",
          type: "action",
          onPress: () => setShowTroubleshooting(true),
        },
        {
          icon: "help-circle",
          title: "Help & FAQ",
          subtitle: "Get help and find answers",
          type: "navigation",
        },
        {
          icon: "refresh",
          title: "Reset Settings",
          subtitle: "Reset all preferences to defaults",
          type: "action",
          onPress: handleResetPreferences,
        },
        {
          icon: "bug",
          title: "Report a Bug",
          subtitle: "Help us improve the app",
          type: "navigation",
        },
        {
          icon: "star",
          title: "Rate the App",
          subtitle: "Share your feedback",
          type: "navigation",
        },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    return (
      <TouchableOpacity key={item.title} style={styles.settingItem}>
        <View style={styles.settingIcon}>
          <Ionicons name={item.icon} size={20} color="#00D4FF" />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
        </View>
        <View style={styles.settingAction}>
          {item.type === "switch" ? (
            <Switch
              value={item.value}
              onValueChange={item.onToggle}
              trackColor={{ false: "#767577", true: "#00D4FF" }}
              thumbColor={item.value ? "#FFFFFF" : "#f4f3f4"}
            />
          ) : (
            <Ionicons
              name="chevron-forward"
              size={20}
              color="rgba(255, 255, 255, 0.4)"
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Settings</Text>
          </View>

          {/* Profile Section */}
          <BlurView intensity={15} style={styles.profileCard}>
            <View style={styles.profileContent}>
              <View style={styles.avatar}>
                <Ionicons name="person" size={32} color="#00D4FF" />
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>Your Device</Text>
                <Text style={styles.profileSubtitle}>iPhone 15 Pro</Text>
              </View>
              <TouchableOpacity style={styles.editButton}>
                <Ionicons name="pencil" size={20} color="#00D4FF" />
              </TouchableOpacity>
            </View>
          </BlurView>

          {/* Settings Groups */}
          {settingsGroups.map((group) => (
            <View key={group.title} style={styles.settingsGroup}>
              <Text style={styles.groupTitle}>{group.title}</Text>
              <BlurView intensity={15} style={styles.groupCard}>
                {group.items.map((item, index) => (
                  <View key={item.title}>
                    {renderSettingItem(item)}
                    {index < group.items.length - 1 && (
                      <View style={styles.separator} />
                    )}
                  </View>
                ))}
              </BlurView>
            </View>
          ))}

          {/* App Info */}
          <View style={styles.appInfo}>
            <Text style={styles.appVersion}>LoGaCo v1.0.0</Text>
            <Text style={styles.appSubtitle}>Local Game Connect</Text>
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* Edit Device Name Modal */}
      <Modal
        visible={showEditName}
        onClose={() => setShowEditName(false)}
        title="Edit Device Name"
        variant="center"
      >
        <View style={styles.editNameContent}>
          <Input
            label="Device Name"
            value={newDeviceName}
            onChangeText={setNewDeviceName}
            placeholder="Enter device name"
            maxLength={20}
          />
          <View style={styles.editNameActions}>
            <Button
              title="Cancel"
              onPress={() => setShowEditName(false)}
              variant="ghost"
              size="medium"
              style={styles.editNameButton}
            />
            <Button
              title="Save"
              onPress={handleSaveDeviceName}
              variant="primary"
              size="medium"
              style={styles.editNameButton}
            />
          </View>
        </View>
      </Modal>

      {/* Troubleshooting Modal */}
      <Modal
        visible={showTroubleshooting}
        onClose={() => setShowTroubleshooting(false)}
        variant="fullscreen"
      >
        <TroubleshootingPanel onClose={() => setShowTroubleshooting(false)} />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  profileCard: {
    marginHorizontal: 20,
    marginBottom: 30,
    borderRadius: 16,
    overflow: "hidden",
    padding: 20,
  },
  profileContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  profileSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  editButton: {
    padding: 8,
  },
  settingsGroup: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "rgba(255, 255, 255, 0.8)",
    marginBottom: 12,
    marginLeft: 4,
  },
  groupCard: {
    borderRadius: 16,
    overflow: "hidden",
    padding: 4,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#FFFFFF",
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  settingAction: {
    marginLeft: 16,
  },
  separator: {
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    marginLeft: 68,
  },
  appInfo: {
    alignItems: "center",
    paddingVertical: 20,
  },
  appVersion: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  appSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
});

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ViewStyle,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { GameInfo } from '../../services/game/GameDetector';
import StatusIndicator from '../ui/StatusIndicator';

interface GameCardProps {
  game: GameInfo;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'compact' | 'featured';
  showCompatibility?: boolean;
  compatibilityScore?: number;
  showLastPlayed?: boolean;
  showPlaytime?: boolean;
}

export default function GameCard({
  game,
  onPress,
  onLongPress,
  style,
  variant = 'default',
  showCompatibility = false,
  compatibilityScore,
  showLastPlayed = false,
  showPlaytime = false,
}: GameCardProps) {
  const formatPlaytime = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`;
    } else if (minutes < 1440) {
      return `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      return `${days}d ${hours}h`;
    }
  };

  const formatLastPlayed = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return 'Today';
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else {
      return `${Math.floor(days / 7)} weeks ago`;
    }
  };

  const getMultiplayerIcon = () => {
    switch (game.multiplayerType) {
      case 'local':
        return 'bluetooth';
      case 'online':
        return 'globe';
      case 'both':
        return 'wifi';
      default:
        return 'person';
    }
  };

  const getCompatibilityStatus = () => {
    if (!showCompatibility || compatibilityScore === undefined) {
      return null;
    }

    if (compatibilityScore >= 80) {
      return { status: 'success', text: 'Excellent' };
    } else if (compatibilityScore >= 60) {
      return { status: 'warning', text: 'Good' };
    } else {
      return { status: 'error', text: 'Issues' };
    }
  };

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[styles.compactCard, style]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.compactBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.compactGradient}
        >
          <View style={styles.compactContent}>
            <View style={styles.compactIcon}>
              <Text style={styles.gameEmoji}>{game.icon || '🎮'}</Text>
            </View>
            
            <View style={styles.compactInfo}>
              <Text style={styles.compactTitle} numberOfLines={1}>
                {game.name}
              </Text>
              <Text style={styles.compactCategory} numberOfLines={1}>
                {game.category}
              </Text>
            </View>

            {game.supportsMultiplayer && (
              <View style={styles.compactMultiplayer}>
                <Ionicons
                  name={getMultiplayerIcon() as any}
                  size={16}
                  color="#00D4FF"
                />
              </View>
            )}
          </View>
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  const renderFeaturedCard = () => (
    <TouchableOpacity
      style={[styles.featuredCard, style]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={20} style={styles.featuredBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.15)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.featuredGradient}
        >
          <View style={styles.featuredHeader}>
            <View style={styles.featuredIcon}>
              <Text style={styles.featuredEmoji}>{game.icon || '🎮'}</Text>
            </View>
            
            {game.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={styles.ratingText}>{game.rating.toFixed(1)}</Text>
              </View>
            )}
          </View>

          <View style={styles.featuredContent}>
            <Text style={styles.featuredTitle}>{game.name}</Text>
            <Text style={styles.featuredDeveloper}>{game.developer}</Text>
            
            {game.description && (
              <Text style={styles.featuredDescription} numberOfLines={2}>
                {game.description}
              </Text>
            )}

            <View style={styles.featuredTags}>
              <View style={styles.tag}>
                <Text style={styles.tagText}>{game.category}</Text>
              </View>
              
              {game.supportsMultiplayer && (
                <View style={styles.multiplayerTag}>
                  <Ionicons
                    name={getMultiplayerIcon() as any}
                    size={12}
                    color="#00D4FF"
                  />
                  <Text style={styles.multiplayerTagText}>
                    {game.multiplayerType}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  const renderDefaultCard = () => (
    <TouchableOpacity
      style={[styles.defaultCard, style]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.8}
    >
      <BlurView intensity={15} style={styles.defaultBlur}>
        <LinearGradient
          colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
          style={styles.defaultGradient}
        >
          <View style={styles.defaultHeader}>
            <View style={styles.defaultIcon}>
              <Text style={styles.gameEmoji}>{game.icon || '🎮'}</Text>
            </View>
            
            <View style={styles.defaultInfo}>
              <Text style={styles.defaultTitle} numberOfLines={1}>
                {game.name}
              </Text>
              <Text style={styles.defaultDeveloper} numberOfLines={1}>
                {game.developer}
              </Text>
            </View>

            {showCompatibility && (
              <View style={styles.compatibilityContainer}>
                <StatusIndicator
                  status={getCompatibilityStatus()?.status as any}
                  label={getCompatibilityStatus()?.text}
                  size="small"
                  variant="badge"
                />
              </View>
            )}
          </View>

          <View style={styles.defaultContent}>
            <View style={styles.gameStats}>
              <View style={styles.statItem}>
                <Ionicons name="folder" size={14} color="#00D4FF" />
                <Text style={styles.statText}>{game.category}</Text>
              </View>
              
              {game.supportsMultiplayer && (
                <View style={styles.statItem}>
                  <Ionicons
                    name={getMultiplayerIcon() as any}
                    size={14}
                    color="#00D4FF"
                  />
                  <Text style={styles.statText}>
                    {game.requirements?.minPlayers}-{game.requirements?.maxPlayers} players
                  </Text>
                </View>
              )}

              {showPlaytime && game.playtime && (
                <View style={styles.statItem}>
                  <Ionicons name="time" size={14} color="#00D4FF" />
                  <Text style={styles.statText}>
                    {formatPlaytime(game.playtime)}
                  </Text>
                </View>
              )}

              {showLastPlayed && game.lastPlayed && (
                <View style={styles.statItem}>
                  <Ionicons name="calendar" size={14} color="#00D4FF" />
                  <Text style={styles.statText}>
                    {formatLastPlayed(game.lastPlayed)}
                  </Text>
                </View>
              )}
            </View>

            {game.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color="#FFD700" />
                <Text style={styles.ratingText}>{game.rating.toFixed(1)}</Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </BlurView>
    </TouchableOpacity>
  );

  switch (variant) {
    case 'compact':
      return renderCompactCard();
    case 'featured':
      return renderFeaturedCard();
    default:
      return renderDefaultCard();
  }
}

const styles = StyleSheet.create({
  // Compact variant styles
  compactCard: {
    marginVertical: 4,
  },
  compactBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  compactGradient: {
    padding: 12,
  },
  compactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  compactIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactInfo: {
    flex: 1,
  },
  compactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  compactCategory: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },
  compactMultiplayer: {
    padding: 4,
  },

  // Featured variant styles
  featuredCard: {
    marginVertical: 8,
  },
  featuredBlur: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  featuredGradient: {
    padding: 20,
  },
  featuredHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  featuredIcon: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredEmoji: {
    fontSize: 32,
  },
  featuredContent: {
    gap: 8,
  },
  featuredTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  featuredDeveloper: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  featuredDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 20,
    marginTop: 4,
  },
  featuredTags: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },

  // Default variant styles
  defaultCard: {
    marginVertical: 6,
  },
  defaultBlur: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  defaultGradient: {
    padding: 16,
  },
  defaultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  defaultIcon: {
    width: 48,
    height: 48,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 212, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultInfo: {
    flex: 1,
  },
  defaultTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  defaultDeveloper: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginTop: 2,
  },
  defaultContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },

  // Shared styles
  gameEmoji: {
    fontSize: 24,
  },
  gameStats: {
    flex: 1,
    gap: 6,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFD700',
  },
  tag: {
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: '#00D4FF',
    fontWeight: '500',
  },
  multiplayerTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'rgba(0, 212, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  multiplayerTagText: {
    fontSize: 12,
    color: '#00D4FF',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  compatibilityContainer: {
    marginLeft: 8,
  },
});

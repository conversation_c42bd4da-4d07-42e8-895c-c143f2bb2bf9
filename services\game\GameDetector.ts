import { Platform } from 'react-native';

export interface GameInfo {
  id: string;
  name: string;
  packageName: string;
  version?: string;
  icon?: string;
  isInstalled: boolean;
  supportsMultiplayer: boolean;
  multiplayerType: 'local' | 'online' | 'both' | 'none';
  category: string;
  developer: string;
  lastPlayed?: number;
  playtime?: number; // in minutes
  rating?: number; // 1-5 stars
  description?: string;
  screenshots?: string[];
  requirements?: {
    minPlayers: number;
    maxPlayers: number;
    networkRequired: boolean;
    bluetoothRequired: boolean;
  };
}

export interface GameDatabase {
  [packageName: string]: Omit<GameInfo, 'isInstalled' | 'version' | 'lastPlayed' | 'playtime'>;
}

class GameDetector {
  private static instance: GameDetector;
  private gameDatabase: GameDatabase = {};
  private installedGames: GameInfo[] = [];

  static getInstance(): GameDetector {
    if (!GameDetector.instance) {
      GameDetector.instance = new GameDetector();
    }
    return GameDetector.instance;
  }

  constructor() {
    this.initializeGameDatabase();
  }

  private initializeGameDatabase() {
    // Popular mobile games that support local multiplayer
    this.gameDatabase = {
      'com.mojang.minecraftpe': {
        id: 'minecraft',
        name: 'Minecraft',
        packageName: 'com.mojang.minecraftpe',
        icon: '🎮',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Sandbox',
        developer: 'Mojang Studios',
        rating: 4.5,
        description: 'Build, explore, and survive in infinite worlds',
        requirements: {
          minPlayers: 1,
          maxPlayers: 8,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.supercell.clashofclans': {
        id: 'clash-of-clans',
        name: 'Clash of Clans',
        packageName: 'com.supercell.clashofclans',
        icon: '⚔️',
        supportsMultiplayer: true,
        multiplayerType: 'online',
        category: 'Strategy',
        developer: 'Supercell',
        rating: 4.3,
        description: 'Build your village and battle with millions of players',
        requirements: {
          minPlayers: 1,
          maxPlayers: 50,
          networkRequired: true,
          bluetoothRequired: false,
        },
      },
      'com.king.candycrushsaga': {
        id: 'candy-crush',
        name: 'Candy Crush Saga',
        packageName: 'com.king.candycrushsaga',
        icon: '🍭',
        supportsMultiplayer: false,
        multiplayerType: 'none',
        category: 'Puzzle',
        developer: 'King',
        rating: 4.1,
        description: 'Match candies in this sweet puzzle adventure',
        requirements: {
          minPlayers: 1,
          maxPlayers: 1,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.ea.game.pvzfree_row': {
        id: 'plants-vs-zombies',
        name: 'Plants vs. Zombies',
        packageName: 'com.ea.game.pvzfree_row',
        icon: '🌱',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Tower Defense',
        developer: 'Electronic Arts',
        rating: 4.4,
        description: 'Defend your home from zombie invasion',
        requirements: {
          minPlayers: 1,
          maxPlayers: 2,
          networkRequired: false,
          bluetoothRequired: true,
        },
      },
      'com.roblox.client': {
        id: 'roblox',
        name: 'Roblox',
        packageName: 'com.roblox.client',
        icon: '🎯',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Social',
        developer: 'Roblox Corporation',
        rating: 4.2,
        description: 'Play millions of user-created games',
        requirements: {
          minPlayers: 1,
          maxPlayers: 100,
          networkRequired: true,
          bluetoothRequired: false,
        },
      },
      'com.innersloth.spacemafia': {
        id: 'among-us',
        name: 'Among Us',
        packageName: 'com.innersloth.spacemafia',
        icon: '👾',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Social Deduction',
        developer: 'InnerSloth LLC',
        rating: 4.0,
        description: 'Find the impostor among your crewmates',
        requirements: {
          minPlayers: 4,
          maxPlayers: 15,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.chess.com': {
        id: 'chess-com',
        name: 'Chess.com',
        packageName: 'com.chess.com',
        icon: '♟️',
        supportsMultiplayer: true,
        multiplayerType: 'both',
        category: 'Board Game',
        developer: 'Chess.com',
        rating: 4.6,
        description: 'Play chess with friends and players worldwide',
        requirements: {
          minPlayers: 2,
          maxPlayers: 2,
          networkRequired: false,
          bluetoothRequired: false,
        },
      },
      'com.scopely.monopolygo': {
        id: 'monopoly-go',
        name: 'Monopoly GO!',
        packageName: 'com.scopely.monopolygo',
        icon: '🎲',
        supportsMultiplayer: true,
        multiplayerType: 'local',
        category: 'Board Game',
        developer: 'Scopely',
        rating: 4.3,
        description: 'The classic board game on mobile',
        requirements: {
          minPlayers: 2,
          maxPlayers: 6,
          networkRequired: false,
          bluetoothRequired: true,
        },
      },
    };
  }

  async scanForGames(): Promise<GameInfo[]> {
    // In a real implementation, this would scan the device for installed apps
    // For now, we'll simulate finding some games
    const mockInstalledPackages = [
      'com.mojang.minecraftpe',
      'com.king.candycrushsaga',
      'com.innersloth.spacemafia',
      'com.chess.com',
    ];

    this.installedGames = mockInstalledPackages
      .map(packageName => {
        const gameData = this.gameDatabase[packageName];
        if (gameData) {
          return {
            ...gameData,
            isInstalled: true,
            version: this.generateMockVersion(),
            lastPlayed: this.generateMockLastPlayed(),
            playtime: this.generateMockPlaytime(),
          };
        }
        return null;
      })
      .filter((game): game is GameInfo => game !== null);

    return this.installedGames;
  }

  async getInstalledGames(): Promise<GameInfo[]> {
    if (this.installedGames.length === 0) {
      await this.scanForGames();
    }
    return this.installedGames;
  }

  async getMultiplayerGames(): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(game => game.supportsMultiplayer);
  }

  async getLocalMultiplayerGames(): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(game => 
      game.supportsMultiplayer && 
      (game.multiplayerType === 'local' || game.multiplayerType === 'both')
    );
  }

  async getGamesByCategory(category: string): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games.filter(game => game.category.toLowerCase() === category.toLowerCase());
  }

  async searchGames(query: string): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    const lowercaseQuery = query.toLowerCase();
    
    return games.filter(game =>
      game.name.toLowerCase().includes(lowercaseQuery) ||
      game.category.toLowerCase().includes(lowercaseQuery) ||
      game.developer.toLowerCase().includes(lowercaseQuery)
    );
  }

  async getGameInfo(packageName: string): Promise<GameInfo | null> {
    const games = await this.getInstalledGames();
    return games.find(game => game.packageName === packageName) || null;
  }

  async isGameInstalled(packageName: string): Promise<boolean> {
    const games = await this.getInstalledGames();
    return games.some(game => game.packageName === packageName);
  }

  async getCompatibleGames(requirements: {
    minPlayers?: number;
    maxPlayers?: number;
    networkAvailable?: boolean;
    bluetoothAvailable?: boolean;
  }): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    
    return games.filter(game => {
      if (!game.supportsMultiplayer) return false;
      
      const gameReqs = game.requirements;
      if (!gameReqs) return true;
      
      // Check player count requirements
      if (requirements.minPlayers && gameReqs.maxPlayers < requirements.minPlayers) {
        return false;
      }
      if (requirements.maxPlayers && gameReqs.minPlayers > requirements.maxPlayers) {
        return false;
      }
      
      // Check network requirements
      if (gameReqs.networkRequired && !requirements.networkAvailable) {
        return false;
      }
      
      // Check Bluetooth requirements
      if (gameReqs.bluetoothRequired && !requirements.bluetoothAvailable) {
        return false;
      }
      
      return true;
    });
  }

  async updateGamePlaytime(packageName: string, additionalMinutes: number): Promise<void> {
    const gameIndex = this.installedGames.findIndex(game => game.packageName === packageName);
    if (gameIndex !== -1) {
      this.installedGames[gameIndex].playtime = 
        (this.installedGames[gameIndex].playtime || 0) + additionalMinutes;
      this.installedGames[gameIndex].lastPlayed = Date.now();
    }
  }

  async getGameCategories(): Promise<string[]> {
    const games = await this.getInstalledGames();
    const categories = new Set(games.map(game => game.category));
    return Array.from(categories).sort();
  }

  async getRecentlyPlayedGames(limit: number = 5): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games
      .filter(game => game.lastPlayed)
      .sort((a, b) => (b.lastPlayed || 0) - (a.lastPlayed || 0))
      .slice(0, limit);
  }

  async getMostPlayedGames(limit: number = 5): Promise<GameInfo[]> {
    const games = await this.getInstalledGames();
    return games
      .filter(game => game.playtime && game.playtime > 0)
      .sort((a, b) => (b.playtime || 0) - (a.playtime || 0))
      .slice(0, limit);
  }

  private generateMockVersion(): string {
    const major = Math.floor(Math.random() * 5) + 1;
    const minor = Math.floor(Math.random() * 10);
    const patch = Math.floor(Math.random() * 20);
    return `${major}.${minor}.${patch}`;
  }

  private generateMockLastPlayed(): number {
    // Random time within the last 30 days
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    return Math.floor(Math.random() * (now - thirtyDaysAgo)) + thirtyDaysAgo;
  }

  private generateMockPlaytime(): number {
    // Random playtime between 10 minutes and 500 hours
    return Math.floor(Math.random() * 30000) + 10;
  }
}

export default GameDetector.getInstance();
